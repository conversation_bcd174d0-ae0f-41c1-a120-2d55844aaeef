import 'dart:io';
import 'package:flutter_test/flutter_test.dart';
import 'package:scholara_student/features/files/models/file_model.dart';
import 'package:scholara_student/features/files/enums/file_type.dart';
import 'package:scholara_student/features/files/enums/download_state.dart';
import 'package:scholara_student/features/files/enums/viewer_type.dart';
import 'package:scholara_student/features/files/services/file_viewer_service.dart';
import 'package:scholara_student/features/files/services/file_download_service.dart';
import 'package:scholara_student/features/files/services/file_cache_service.dart';

void main() {
  group('FileModel Tests', () {
    test('should create FileModel from local file', () {
      // Create a temporary file for testing
      final tempFile = File('test_file.pdf');
      
      final fileModel = FileModel.fromLocalFile(tempFile);
      
      expect(fileModel.fileName, equals('test_file.pdf'));
      expect(fileModel.fileExtension, equals('pdf'));
      expect(fileModel.fileType, equals(FileType.pdf));
      expect(fileModel.isLocal, isTrue);
      expect(fileModel.downloadState, equals(DownloadState.downloaded));
    });

    test('should create FileModel from URL', () {
      const url = 'https://example.com/document.pdf';
      
      final fileModel = FileModel.fromUrl(url);
      
      expect(fileModel.fileName, equals('document.pdf'));
      expect(fileModel.fileExtension, equals('pdf'));
      expect(fileModel.fileType, equals(FileType.pdf));
      expect(fileModel.isLocal, isFalse);
      expect(fileModel.url, equals(url));
      expect(fileModel.downloadState, equals(DownloadState.notDownloaded));
    });

    test('should determine correct viewer type', () {
      final imageFile = FileModel.fromUrl('https://example.com/image.jpg');
      final pdfFile = FileModel.fromUrl('https://example.com/document.pdf');
      final docFile = FileModel.fromUrl('https://example.com/document.docx');
      
      expect(imageFile.viewerType, equals(ViewerType.image));
      expect(pdfFile.viewerType, equals(ViewerType.pdf));
      expect(docFile.viewerType, equals(ViewerType.placeholder));
    });

    test('should check availability for viewing', () {
      final localFile = FileModel.fromLocalFile(File('test.pdf'));
      final remoteFile = FileModel.fromUrl('https://example.com/test.pdf');
      final downloadedFile = remoteFile.copyWith(
        downloadState: DownloadState.downloaded,
        localPath: '/path/to/downloaded/file.pdf',
      );
      
      expect(localFile.isAvailableForViewing, isTrue);
      expect(remoteFile.isAvailableForViewing, isFalse);
      expect(downloadedFile.isAvailableForViewing, isTrue);
    });
  });

  group('FileType Detection Tests', () {
    test('should detect image file types', () {
      expect(FileTypeDetector.fromExtension('jpg'), equals(FileType.image));
      expect(FileTypeDetector.fromExtension('png'), equals(FileType.image));
      expect(FileTypeDetector.fromExtension('gif'), equals(FileType.image));
      expect(FileTypeDetector.fromExtension('webp'), equals(FileType.image));
    });

    test('should detect PDF file type', () {
      expect(FileTypeDetector.fromExtension('pdf'), equals(FileType.pdf));
    });

    test('should detect document file types', () {
      expect(FileTypeDetector.fromExtension('doc'), equals(FileType.document));
      expect(FileTypeDetector.fromExtension('docx'), equals(FileType.document));
      expect(FileTypeDetector.fromExtension('txt'), equals(FileType.document));
    });

    test('should detect video file types', () {
      expect(FileTypeDetector.fromExtension('mp4'), equals(FileType.video));
      expect(FileTypeDetector.fromExtension('mov'), equals(FileType.video));
      expect(FileTypeDetector.fromExtension('avi'), equals(FileType.video));
    });

    test('should detect audio file types', () {
      expect(FileTypeDetector.fromExtension('mp3'), equals(FileType.audio));
      expect(FileTypeDetector.fromExtension('wav'), equals(FileType.audio));
      expect(FileTypeDetector.fromExtension('aac'), equals(FileType.audio));
    });

    test('should detect archive file types', () {
      expect(FileTypeDetector.fromExtension('zip'), equals(FileType.archive));
      expect(FileTypeDetector.fromExtension('rar'), equals(FileType.archive));
      expect(FileTypeDetector.fromExtension('7z'), equals(FileType.archive));
    });

    test('should return other for unknown file types', () {
      expect(FileTypeDetector.fromExtension('xyz'), equals(FileType.other));
      expect(FileTypeDetector.fromExtension(''), equals(FileType.other));
    });

    test('should detect file type from file name', () {
      expect(FileTypeDetector.fromFileName('document.pdf'), equals(FileType.pdf));
      expect(FileTypeDetector.fromFileName('image.jpg'), equals(FileType.image));
      expect(FileTypeDetector.fromFileName('video.mp4'), equals(FileType.video));
      expect(FileTypeDetector.fromFileName('no_extension'), equals(FileType.other));
    });

    test('should detect file type from path', () {
      expect(
        FileTypeDetector.fromPath('/path/to/document.pdf'),
        equals(FileType.pdf),
      );
      expect(
        FileTypeDetector.fromPath('https://example.com/image.jpg'),
        equals(FileType.image),
      );
    });
  });

  group('FileType Extension Tests', () {
    test('should provide correct labels', () {
      expect(FileType.image.label, equals('Image'));
      expect(FileType.pdf.label, equals('PDF'));
      expect(FileType.video.label, equals('Video'));
      expect(FileType.document.label, equals('Document'));
      expect(FileType.audio.label, equals('Audio'));
      expect(FileType.archive.label, equals('Archive'));
      expect(FileType.other.label, equals('File'));
    });

    test('should provide correct color hex values', () {
      expect(FileType.image.colorHex, equals('#4CAF50'));
      expect(FileType.pdf.colorHex, equals('#F44336'));
      expect(FileType.video.colorHex, equals('#FF5722'));
      expect(FileType.document.colorHex, equals('#2196F3'));
      expect(FileType.audio.colorHex, equals('#9C27B0'));
      expect(FileType.archive.colorHex, equals('#FF9800'));
      expect(FileType.other.colorHex, equals('#9E9E9E'));
    });

    test('should correctly identify native viewing capability', () {
      expect(FileType.image.canViewNatively, isTrue);
      expect(FileType.pdf.canViewNatively, isTrue);
      expect(FileType.video.canViewNatively, isFalse);
      expect(FileType.document.canViewNatively, isFalse);
      expect(FileType.audio.canViewNatively, isFalse);
      expect(FileType.archive.canViewNatively, isFalse);
      expect(FileType.other.canViewNatively, isFalse);
    });

    test('should provide correct file extensions', () {
      expect(FileType.image.extensions, contains('jpg'));
      expect(FileType.image.extensions, contains('png'));
      expect(FileType.pdf.extensions, contains('pdf'));
      expect(FileType.video.extensions, contains('mp4'));
      expect(FileType.document.extensions, contains('doc'));
      expect(FileType.audio.extensions, contains('mp3'));
      expect(FileType.archive.extensions, contains('zip'));
    });
  });

  group('ViewerType Extension Tests', () {
    test('should provide correct labels', () {
      expect(ViewerType.image.label, equals('Image Viewer'));
      expect(ViewerType.pdf.label, equals('PDF Viewer'));
      expect(ViewerType.webView.label, equals('Web Viewer'));
      expect(ViewerType.placeholder.label, equals('File Preview'));
    });

    test('should correctly identify full-screen support', () {
      expect(ViewerType.image.supportsFullScreen, isTrue);
      expect(ViewerType.pdf.supportsFullScreen, isTrue);
      expect(ViewerType.webView.supportsFullScreen, isFalse);
      expect(ViewerType.placeholder.supportsFullScreen, isFalse);
    });

    test('should correctly identify zoom support', () {
      expect(ViewerType.image.supportsZoom, isTrue);
      expect(ViewerType.pdf.supportsZoom, isFalse);
      expect(ViewerType.webView.supportsZoom, isFalse);
      expect(ViewerType.placeholder.supportsZoom, isFalse);
    });

    test('should correctly identify sharing support', () {
      expect(ViewerType.image.supportsSharing, isTrue);
      expect(ViewerType.pdf.supportsSharing, isTrue);
      expect(ViewerType.webView.supportsSharing, isFalse);
      expect(ViewerType.placeholder.supportsSharing, isFalse);
    });
  });

  group('DownloadState Extension Tests', () {
    test('should provide correct labels', () {
      expect(DownloadState.notDownloaded.label, equals('Not Downloaded'));
      expect(DownloadState.downloading.label, equals('Downloading'));
      expect(DownloadState.downloaded.label, equals('Downloaded'));
      expect(DownloadState.failed.label, equals('Download Failed'));
      expect(DownloadState.cancelled.label, equals('Download Cancelled'));
      expect(DownloadState.paused.label, equals('Download Paused'));
    });

    test('should correctly identify in-progress states', () {
      expect(DownloadState.downloading.isInProgress, isTrue);
      expect(DownloadState.paused.isInProgress, isTrue);
      expect(DownloadState.notDownloaded.isInProgress, isFalse);
      expect(DownloadState.downloaded.isInProgress, isFalse);
      expect(DownloadState.failed.isInProgress, isFalse);
      expect(DownloadState.cancelled.isInProgress, isFalse);
    });

    test('should correctly identify complete state', () {
      expect(DownloadState.downloaded.isComplete, isTrue);
      expect(DownloadState.downloading.isComplete, isFalse);
      expect(DownloadState.failed.isComplete, isFalse);
    });

    test('should correctly identify failed states', () {
      expect(DownloadState.failed.hasFailed, isTrue);
      expect(DownloadState.cancelled.hasFailed, isTrue);
      expect(DownloadState.downloaded.hasFailed, isFalse);
      expect(DownloadState.downloading.hasFailed, isFalse);
    });

    test('should correctly identify retry capability', () {
      expect(DownloadState.failed.canRetry, isTrue);
      expect(DownloadState.cancelled.canRetry, isTrue);
      expect(DownloadState.downloaded.canRetry, isFalse);
      expect(DownloadState.downloading.canRetry, isFalse);
    });

    test('should correctly identify pause/resume capability', () {
      expect(DownloadState.downloading.canPause, isTrue);
      expect(DownloadState.paused.canResume, isTrue);
      expect(DownloadState.downloaded.canPause, isFalse);
      expect(DownloadState.failed.canResume, isFalse);
    });

    test('should correctly identify cancel capability', () {
      expect(DownloadState.downloading.canCancel, isTrue);
      expect(DownloadState.paused.canCancel, isTrue);
      expect(DownloadState.downloaded.canCancel, isFalse);
      expect(DownloadState.failed.canCancel, isFalse);
    });
  });

  group('FileViewerService Tests', () {
    late FileViewerService fileViewerService;

    setUp(() {
      fileViewerService = FileViewerService();
    });

    test('should create FileModel from URL', () {
      const url = 'https://example.com/test.pdf';
      const customFileName = 'custom_name.pdf';
      
      final fileModel = fileViewerService.createFileModelFromUrl(url);
      final customFileModel = fileViewerService.createFileModelFromUrl(
        url,
        customFileName: customFileName,
      );
      
      expect(fileModel.url, equals(url));
      expect(fileModel.fileName, equals('test.pdf'));
      expect(customFileModel.fileName, equals(customFileName));
    });

    test('should determine viewer type correctly', () {
      final imageFile = FileModel.fromUrl('https://example.com/image.jpg');
      final pdfFile = FileModel.fromUrl('https://example.com/document.pdf');
      final docFile = FileModel.fromUrl('https://example.com/document.docx');
      
      expect(fileViewerService.getViewerType(imageFile), equals(ViewerType.image));
      expect(fileViewerService.getViewerType(pdfFile), equals(ViewerType.pdf));
      expect(fileViewerService.getViewerType(docFile), equals(ViewerType.placeholder));
    });

    test('should check if file can be viewed in app', () {
      final imageFile = FileModel.fromUrl('https://example.com/image.jpg');
      final pdfFile = FileModel.fromUrl('https://example.com/document.pdf');
      final docFile = FileModel.fromUrl('https://example.com/document.docx');
      
      expect(fileViewerService.canViewInApp(imageFile), isTrue);
      expect(fileViewerService.canViewInApp(pdfFile), isTrue);
      expect(fileViewerService.canViewInApp(docFile), isFalse);
    });

    test('should check if file needs download', () {
      final localFile = FileModel.fromLocalFile(File('test.pdf'));
      final remoteFile = FileModel.fromUrl('https://example.com/test.pdf');
      final downloadedFile = remoteFile.copyWith(
        downloadState: DownloadState.downloaded,
        localPath: '/path/to/file.pdf',
      );
      
      expect(fileViewerService.needsDownload(localFile), isFalse);
      expect(fileViewerService.needsDownload(remoteFile), isTrue);
      expect(fileViewerService.needsDownload(downloadedFile), isFalse);
    });
  });
}
