import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:share_plus/share_plus.dart';
import '../models/file_model.dart';
import '../enums/viewer_type.dart';
import '../services/file_viewer_service.dart';
import '../services/file_download_service.dart';
import '../widgets/image_viewer_widget.dart';
import '../widgets/pdf_viewer_widget.dart';
import '../widgets/placeholder_viewer_widget.dart';

/// Full-screen file viewer screen
class FileViewerScreen extends StatefulWidget {
  /// The file to view
  final FileModel fileModel;
  
  /// Optional list of files for navigation
  final List<FileModel>? fileList;
  
  /// Current index in the file list
  final int? currentIndex;

  const FileViewerScreen({
    super.key,
    required this.fileModel,
    this.fileList,
    this.currentIndex,
  });

  @override
  State<FileViewerScreen> createState() => _FileViewerScreenState();
}

class _FileViewerScreenState extends State<FileViewerScreen> {
  late FileModel _currentFile;
  late int _currentIndex;
  bool _isLoading = false;
  String? _errorMessage;
  
  final FileViewerService _viewerService = FileViewerService();
  final FileDownloadService _downloadService = FileDownloadService();

  @override
  void initState() {
    super.initState();
    _currentFile = widget.fileModel;
    _currentIndex = widget.currentIndex ?? 0;
    
    // Set system UI overlay style for full-screen viewing
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    
    _initializeFile();
  }

  @override
  void dispose() {
    // Restore system UI
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  /// Initialize the file for viewing
  Future<void> _initializeFile() async {
    if (_viewerService.needsDownload(_currentFile)) {
      await _downloadFile();
    }
  }

  /// Download the current file
  Future<void> _downloadFile() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final downloadedFile = await _viewerService.downloadForViewing(_currentFile);
      if (downloadedFile != null) {
        setState(() {
          _currentFile = downloadedFile;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to download file';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Error downloading file: $e';
      });
    }
  }

  /// Navigate to previous file
  void _navigateToPrevious() {
    if (widget.fileList != null && _currentIndex > 0) {
      setState(() {
        _currentIndex--;
        _currentFile = widget.fileList![_currentIndex];
        _isLoading = false;
        _errorMessage = null;
      });
      _initializeFile();
    }
  }

  /// Navigate to next file
  void _navigateToNext() {
    if (widget.fileList != null && _currentIndex < widget.fileList!.length - 1) {
      setState(() {
        _currentIndex++;
        _currentFile = widget.fileList![_currentIndex];
        _isLoading = false;
        _errorMessage = null;
      });
      _initializeFile();
    }
  }

  /// Share the current file
  Future<void> _shareFile() async {
    try {
      if (_currentFile.localPath != null) {
        await Share.shareXFiles(
          [XFile(_currentFile.localPath!)],
          text: 'Sharing ${_currentFile.fileName}',
        );
      } else if (_currentFile.url != null) {
        await Share.share(
          _currentFile.url!,
          subject: _currentFile.fileName,
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to share file: $e')),
      );
    }
  }

  /// Download the file to device
  Future<void> _downloadToDevice() async {
    if (_currentFile.isLocal) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('File is already on device')),
      );
      return;
    }

    try {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Starting download...')),
      );
      
      await _downloadService.downloadFile(_currentFile);
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('File downloaded successfully')),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Download failed: $e')),
      );
    }
  }

  /// Open file externally
  Future<void> _openExternally() async {
    try {
      await _viewerService.openFileExternally(_currentFile);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to open externally: $e')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black.withOpacity(0.7),
        foregroundColor: Colors.white,
        title: Text(
          _currentFile.fileName,
          style: theme.textTheme.titleMedium?.copyWith(
            color: Colors.white,
          ),
        ),
        actions: [
          // Navigation controls (if multiple files)
          if (widget.fileList != null && widget.fileList!.length > 1) ...[
            IconButton(
              onPressed: _currentIndex > 0 ? _navigateToPrevious : null,
              icon: const Icon(Symbols.chevron_left),
            ),
            Text(
              '${_currentIndex + 1}/${widget.fileList!.length}',
              style: theme.textTheme.bodyMedium?.copyWith(color: Colors.white),
            ),
            IconButton(
              onPressed: _currentIndex < widget.fileList!.length - 1 
                  ? _navigateToNext 
                  : null,
              icon: const Icon(Symbols.chevron_right),
            ),
          ],
          
          // Action buttons
          PopupMenuButton<String>(
            icon: const Icon(Symbols.more_vert, color: Colors.white),
            onSelected: (value) {
              switch (value) {
                case 'share':
                  _shareFile();
                  break;
                case 'download':
                  _downloadToDevice();
                  break;
                case 'external':
                  _openExternally();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Symbols.share),
                    SizedBox(width: 8),
                    Text('Share'),
                  ],
                ),
              ),
              if (!_currentFile.isLocal)
                const PopupMenuItem(
                  value: 'download',
                  child: Row(
                    children: [
                      Icon(Symbols.download),
                      SizedBox(width: 8),
                      Text('Download'),
                    ],
                  ),
                ),
              const PopupMenuItem(
                value: 'external',
                child: Row(
                  children: [
                    Icon(Symbols.open_in_new),
                    SizedBox(width: 8),
                    Text('Open Externally'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Colors.white),
            SizedBox(height: 16),
            Text(
              'Loading file...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (_errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Symbols.error,
              size: 64,
              color: Colors.red,
            ),
            SizedBox(height: 16.h),
            Text(
              _errorMessage!,
              style: const TextStyle(color: Colors.white),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 16.h),
            ElevatedButton(
              onPressed: _initializeFile,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return _buildViewer();
  }

  Widget _buildViewer() {
    final viewerType = _viewerService.getViewerType(_currentFile);
    
    switch (viewerType) {
      case ViewerType.image:
        return ImageViewerWidget(fileModel: _currentFile);
      case ViewerType.pdf:
        return PDFViewerWidget(fileModel: _currentFile);
      case ViewerType.webView:
      case ViewerType.placeholder:
        return PlaceholderViewerWidget(fileModel: _currentFile);
    }
  }
}
