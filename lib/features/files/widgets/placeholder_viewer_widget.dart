import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import 'package:share_plus/share_plus.dart';
import '../models/file_model.dart';
import '../services/file_viewer_service.dart';
import '../services/file_download_service.dart';

/// Widget for displaying unsupported file types with file info and actions
class PlaceholderViewerWidget extends StatefulWidget {
  /// The file model to display
  final FileModel fileModel;

  const PlaceholderViewerWidget({
    super.key,
    required this.fileModel,
  });

  @override
  State<PlaceholderViewerWidget> createState() => _PlaceholderViewerWidgetState();
}

class _PlaceholderViewerWidgetState extends State<PlaceholderViewerWidget> {
  final FileViewerService _viewerService = FileViewerService();
  final FileDownloadService _downloadService = FileDownloadService();
  bool _isDownloading = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final fileIcon = _viewerService.getFileIcon(widget.fileModel.fileType);
    final fileColor = _viewerService.getFileColor(widget.fileModel.fileType);

    return Container(
      color: Colors.black,
      child: Center(
        child: SingleChildScrollView(
          padding: EdgeInsets.all(24.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // File icon
              Container(
                width: 120.w,
                height: 120.w,
                decoration: BoxDecoration(
                  color: fileColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(16.r),
                  border: Border.all(
                    color: fileColor.withOpacity(0.3),
                    width: 2,
                  ),
                ),
                child: Icon(
                  fileIcon,
                  size: 64.sp,
                  color: fileColor,
                ),
              ),

              SizedBox(height: 24.h),

              // File name
              Text(
                widget.fileModel.fileName,
                style: theme.textTheme.headlineSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),

              SizedBox(height: 8.h),

              // File type
              Container(
                padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
                decoration: BoxDecoration(
                  color: fileColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16.r),
                ),
                child: Text(
                  widget.fileModel.fileType.label.toUpperCase(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: fileColor,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 1.2,
                  ),
                ),
              ),

              SizedBox(height: 24.h),

              // File details
              _buildFileDetails(theme),

              SizedBox(height: 32.h),

              // Action buttons
              _buildActionButtons(theme),

              SizedBox(height: 24.h),

              // Info message
              Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12.r),
                ),
                child: Row(
                  children: [
                    Icon(
                      Symbols.info,
                      color: Colors.white70,
                      size: 20.sp,
                    ),
                    SizedBox(width: 12.w),
                    Expanded(
                      child: Text(
                        'This file type cannot be previewed in the app. You can download it or open it with an external app.',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFileDetails(ThemeData theme) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.05),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: Colors.white.withOpacity(0.1),
        ),
      ),
      child: Column(
        children: [
          // File size
          if (widget.fileModel.fileSizeBytes != null)
            _buildDetailRow(
              'Size',
              widget.fileModel.fileSizeString,
              Symbols.storage,
              theme,
            ),

          // File extension
          if (widget.fileModel.fileExtension.isNotEmpty) ...[
            if (widget.fileModel.fileSizeBytes != null) SizedBox(height: 12.h),
            _buildDetailRow(
              'Type',
              widget.fileModel.fileExtension.toUpperCase(),
              Symbols.description,
              theme,
            ),
          ],

          // Source
          SizedBox(height: 12.h),
          _buildDetailRow(
            'Source',
            widget.fileModel.isLocal ? 'Local File' : 'Remote File',
            widget.fileModel.isLocal ? Symbols.phone_android : Symbols.cloud,
            theme,
          ),

          // Download status
          if (!widget.fileModel.isLocal) ...[
            SizedBox(height: 12.h),
            _buildDetailRow(
              'Status',
              widget.fileModel.downloadState.label,
              widget.fileModel.downloadState.isComplete 
                  ? Symbols.check_circle 
                  : Symbols.cloud_download,
              theme,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value,
    IconData icon,
    ThemeData theme,
  ) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16.sp,
          color: Colors.white54,
        ),
        SizedBox(width: 8.w),
        Text(
          '$label:',
          style: theme.textTheme.bodySmall?.copyWith(
            color: Colors.white54,
          ),
        ),
        SizedBox(width: 8.w),
        Expanded(
          child: Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(ThemeData theme) {
    return Column(
      children: [
        // Download button (for remote files)
        if (!widget.fileModel.isLocal && !widget.fileModel.isAvailableForViewing)
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _isDownloading ? null : _downloadFile,
              icon: _isDownloading
                  ? SizedBox(
                      width: 16.w,
                      height: 16.w,
                      child: const CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Symbols.download),
              label: Text(_isDownloading ? 'Downloading...' : 'Download'),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: EdgeInsets.symmetric(vertical: 12.h),
              ),
            ),
          ),

        // Open externally button
        SizedBox(height: 12.h),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _openExternally,
            icon: const Icon(Symbols.open_in_new),
            label: const Text('Open Externally'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white54),
              padding: EdgeInsets.symmetric(vertical: 12.h),
            ),
          ),
        ),

        // Share button
        SizedBox(height: 12.h),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _shareFile,
            icon: const Icon(Symbols.share),
            label: const Text('Share'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white54),
              padding: EdgeInsets.symmetric(vertical: 12.h),
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _downloadFile() async {
    if (widget.fileModel.url == null) return;

    setState(() {
      _isDownloading = true;
    });

    try {
      await _downloadService.downloadFile(widget.fileModel);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('File downloaded successfully')),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Download failed: $e')),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isDownloading = false;
        });
      }
    }
  }

  Future<void> _openExternally() async {
    try {
      await _viewerService.openFileExternally(widget.fileModel);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open externally: $e')),
        );
      }
    }
  }

  Future<void> _shareFile() async {
    try {
      if (widget.fileModel.localPath != null) {
        await Share.shareXFiles(
          [XFile(widget.fileModel.localPath!)],
          text: 'Sharing ${widget.fileModel.fileName}',
        );
      } else if (widget.fileModel.url != null) {
        await Share.share(
          widget.fileModel.url!,
          subject: widget.fileModel.fileName,
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to share file: $e')),
        );
      }
    }
  }
}
