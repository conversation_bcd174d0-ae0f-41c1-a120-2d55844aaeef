import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/file_model.dart';
import '../enums/download_state.dart';

/// Widget for indicating offline availability of files
class OfflineIndicatorWidget extends StatelessWidget {
  /// The file model to check availability for
  final FileModel fileModel;
  
  /// Size of the indicator
  final double? size;
  
  /// Whether to show text label
  final bool showLabel;
  
  /// Custom color for the indicator
  final Color? color;

  const OfflineIndicatorWidget({
    super.key,
    required this.fileModel,
    this.size,
    this.showLabel = false,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final indicatorSize = size ?? 16.sp;

    if (!_shouldShowIndicator()) {
      return const SizedBox.shrink();
    }

    final indicatorColor = color ?? _getIndicatorColor(colorScheme);
    final icon = _getIndicatorIcon();
    final label = _getIndicatorLabel();

    if (showLabel) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: indicatorSize,
            color: indicatorColor,
          ),
          SizedBox(width: 4.w),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: indicatorColor,
              fontSize: (indicatorSize * 0.8).sp,
            ),
          ),
        ],
      );
    }

    return Icon(
      icon,
      size: indicatorSize,
      color: indicatorColor,
    );
  }

  bool _shouldShowIndicator() {
    // Show indicator for remote files to indicate their availability status
    return !fileModel.isLocal;
  }

  IconData _getIndicatorIcon() {
    if (fileModel.isAvailableForViewing) {
      return Symbols.offline_pin; // Available offline
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return Symbols.cloud_download; // Currently downloading
    } else {
      return Symbols.cloud; // Online only
    }
  }

  Color _getIndicatorColor(ColorScheme colorScheme) {
    if (fileModel.isAvailableForViewing) {
      return Colors.green; // Available offline
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return colorScheme.primary; // Currently downloading
    } else {
      return colorScheme.onSurface.withOpacity(0.6); // Online only
    }
  }

  String _getIndicatorLabel() {
    if (fileModel.isAvailableForViewing) {
      return 'Offline';
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return 'Downloading';
    } else {
      return 'Online';
    }
  }
}

/// Compact offline indicator for use in lists
class CompactOfflineIndicatorWidget extends StatelessWidget {
  /// The file model to check availability for
  final FileModel fileModel;

  const CompactOfflineIndicatorWidget({
    super.key,
    required this.fileModel,
  });

  @override
  Widget build(BuildContext context) {
    return OfflineIndicatorWidget(
      fileModel: fileModel,
      size: 12.sp,
      showLabel: false,
    );
  }
}

/// Detailed offline status widget with description
class DetailedOfflineStatusWidget extends StatelessWidget {
  /// The file model to show status for
  final FileModel fileModel;
  
  /// Whether to show the file size
  final bool showFileSize;

  const DetailedOfflineStatusWidget({
    super.key,
    required this.fileModel,
    this.showFileSize = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: _getBackgroundColor(colorScheme),
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: _getBorderColor(colorScheme),
        ),
      ),
      child: Row(
        children: [
          OfflineIndicatorWidget(
            fileModel: fileModel,
            size: 20.sp,
          ),
          SizedBox(width: 12.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getStatusTitle(),
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: _getTextColor(colorScheme),
                  ),
                ),
                SizedBox(height: 2.h),
                Text(
                  _getStatusDescription(),
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: _getTextColor(colorScheme).withOpacity(0.7),
                  ),
                ),
                if (showFileSize && fileModel.fileSizeBytes != null) ...[
                  SizedBox(height: 2.h),
                  Text(
                    fileModel.fileSizeString,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: _getTextColor(colorScheme).withOpacity(0.5),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Color _getBackgroundColor(ColorScheme colorScheme) {
    if (fileModel.isAvailableForViewing) {
      return Colors.green.withOpacity(0.1);
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return colorScheme.primary.withOpacity(0.1);
    } else {
      return colorScheme.surface;
    }
  }

  Color _getBorderColor(ColorScheme colorScheme) {
    if (fileModel.isAvailableForViewing) {
      return Colors.green.withOpacity(0.3);
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return colorScheme.primary.withOpacity(0.3);
    } else {
      return colorScheme.outline.withOpacity(0.2);
    }
  }

  Color _getTextColor(ColorScheme colorScheme) {
    if (fileModel.isAvailableForViewing) {
      return Colors.green.shade700;
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return colorScheme.primary;
    } else {
      return colorScheme.onSurface;
    }
  }

  String _getStatusTitle() {
    if (fileModel.isAvailableForViewing) {
      return 'Available Offline';
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return 'Downloading';
    } else {
      return 'Online Only';
    }
  }

  String _getStatusDescription() {
    if (fileModel.isAvailableForViewing) {
      return 'This file is saved on your device and can be viewed without internet.';
    } else if (fileModel.downloadState == DownloadState.downloading) {
      return 'File is being downloaded for offline viewing.';
    } else {
      return 'Internet connection required to view this file.';
    }
  }
}

/// Badge-style offline indicator
class OfflineBadgeWidget extends StatelessWidget {
  /// The file model to check availability for
  final FileModel fileModel;

  const OfflineBadgeWidget({
    super.key,
    required this.fileModel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (!fileModel.isAvailableForViewing) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 6.w, vertical: 2.h),
      decoration: BoxDecoration(
        color: Colors.green,
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Symbols.offline_pin,
            size: 10.sp,
            color: Colors.white,
          ),
          SizedBox(width: 2.w),
          Text(
            'OFFLINE',
            style: theme.textTheme.bodySmall?.copyWith(
              color: Colors.white,
              fontSize: 8.sp,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }
}
