import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_pdfview/flutter_pdfview.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/file_model.dart';

/// Widget for viewing PDF files
class PDFViewerWidget extends StatefulWidget {
  /// The file model containing the PDF to view
  final FileModel fileModel;

  const PDFViewerWidget({
    super.key,
    required this.fileModel,
  });

  @override
  State<PDFViewerWidget> createState() => _PDFViewerWidgetState();
}

class _PDFViewerWidgetState extends State<PDFViewerWidget> {
  PDFViewController? _pdfViewController;
  bool _isLoading = true;
  String? _errorMessage;
  int _currentPage = 1;
  int _totalPages = 0;
  bool _showControls = true;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Check if we have a valid local path for the PDF
    if (widget.fileModel.localPath == null) {
      return _buildErrorView('PDF file not available locally');
    }

    if (!File(widget.fileModel.localPath!).existsSync()) {
      return _buildErrorView('PDF file not found');
    }

    return Stack(
      children: [
        // PDF Viewer
        PDFView(
          filePath: widget.fileModel.localPath!,
          enableSwipe: true,
          swipeHorizontal: false,
          autoSpacing: true,
          pageFling: true,
          pageSnap: true,
          defaultPage: 0,
          fitPolicy: FitPolicy.BOTH,
          preventLinkNavigation: false,
          backgroundColor: Colors.black,
          onRender: (pages) {
            setState(() {
              _totalPages = pages ?? 0;
              _isLoading = false;
            });
          },
          onError: (error) {
            setState(() {
              _isLoading = false;
              _errorMessage = 'Failed to load PDF: $error';
            });
          },
          onPageError: (page, error) {
            setState(() {
              _errorMessage = 'Error loading page $page: $error';
            });
          },
          onViewCreated: (PDFViewController pdfViewController) {
            _pdfViewController = pdfViewController;
          },
          onPageChanged: (page, total) {
            setState(() {
              _currentPage = (page ?? 0) + 1;
              _totalPages = total ?? 0;
            });
          },
        ),

        // Loading overlay
        if (_isLoading)
          Container(
            color: Colors.black54,
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'Loading PDF...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),

        // Error overlay
        if (_errorMessage != null)
          Container(
            color: Colors.black54,
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Symbols.error,
                    size: 64,
                    color: Colors.red,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _errorMessage!,
                    style: const TextStyle(color: Colors.white),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _isLoading = true;
                        _errorMessage = null;
                      });
                    },
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),

        // Controls overlay
        if (_showControls && !_isLoading && _errorMessage == null)
          _buildControlsOverlay(theme),

        // Tap detector to show/hide controls
        GestureDetector(
          onTap: () {
            setState(() {
              _showControls = !_showControls;
            });
          },
          behavior: HitTestBehavior.translucent,
          child: Container(),
        ),
      ],
    );
  }

  Widget _buildControlsOverlay(ThemeData theme) {
    return Column(
      children: [
        // Top controls
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                Colors.black.withOpacity(0.7),
                Colors.transparent,
              ],
            ),
          ),
          child: SafeArea(
            child: Row(
              children: [
                Text(
                  widget.fileModel.fileName,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                  ),
                ),
                const Spacer(),
                if (_totalPages > 0)
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(4.r),
                    ),
                    child: Text(
                      '$_currentPage / $_totalPages',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),

        const Spacer(),

        // Bottom controls
        Container(
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              colors: [
                Colors.black.withOpacity(0.7),
                Colors.transparent,
              ],
            ),
          ),
          child: SafeArea(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                // Previous page
                IconButton(
                  onPressed: _currentPage > 1 ? _goToPreviousPage : null,
                  icon: const Icon(Symbols.chevron_left),
                  color: Colors.white,
                  iconSize: 32.sp,
                ),

                // Page input
                GestureDetector(
                  onTap: _showPageSelector,
                  child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
                    decoration: BoxDecoration(
                      color: Colors.black54,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: Colors.white24),
                    ),
                    child: Text(
                      'Page $_currentPage',
                      style: theme.textTheme.bodyMedium?.copyWith(
                        color: Colors.white,
                      ),
                    ),
                  ),
                ),

                // Next page
                IconButton(
                  onPressed: _currentPage < _totalPages ? _goToNextPage : null,
                  icon: const Icon(Symbols.chevron_right),
                  color: Colors.white,
                  iconSize: 32.sp,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            Symbols.picture_as_pdf,
            size: 64,
            color: Colors.white54,
          ),
          SizedBox(height: 16.h),
          Text(
            message,
            style: const TextStyle(color: Colors.white),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          Text(
            widget.fileModel.fileName,
            style: const TextStyle(color: Colors.white38),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _goToPreviousPage() {
    if (_pdfViewController != null && _currentPage > 1) {
      _pdfViewController!.setPage(_currentPage - 2); // 0-indexed
    }
  }

  void _goToNextPage() {
    if (_pdfViewController != null && _currentPage < _totalPages) {
      _pdfViewController!.setPage(_currentPage); // 0-indexed
    }
  }

  void _showPageSelector() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Go to Page'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Enter page number (1-$_totalPages):'),
            const SizedBox(height: 16),
            TextFormField(
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                border: OutlineInputBorder(),
                hintText: 'Page number',
              ),
              onFieldSubmitted: (value) {
                final pageNumber = int.tryParse(value);
                if (pageNumber != null && 
                    pageNumber >= 1 && 
                    pageNumber <= _totalPages) {
                  _pdfViewController?.setPage(pageNumber - 1); // 0-indexed
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}
