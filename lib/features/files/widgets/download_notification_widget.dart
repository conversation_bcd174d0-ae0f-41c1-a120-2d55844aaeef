import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/download_progress_model.dart';
import '../enums/download_state.dart';

/// Widget for showing download notifications
class DownloadNotificationWidget extends StatelessWidget {
  /// The download progress to display
  final DownloadProgressModel progress;
  
  /// File name being downloaded
  final String fileName;
  
  /// Callback when notification is dismissed
  final VoidCallback? onDismiss;
  
  /// Callback when cancel button is pressed
  final VoidCallback? onCancel;

  const DownloadNotificationWidget({
    super.key,
    required this.progress,
    required this.fileName,
    this.onDismiss,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      margin: EdgeInsets.all(16.w),
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with icon and title
          Row(
            children: [
              _buildStatusIcon(colorScheme),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _getNotificationTitle(),
                      style: theme.textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      fileName,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.7),
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              // Action buttons
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (progress.state.canCancel && onCancel != null)
                    IconButton(
                      onPressed: onCancel,
                      icon: Icon(Symbols.close, size: 18.sp),
                      iconSize: 18.sp,
                      constraints: BoxConstraints(
                        minWidth: 32.w,
                        minHeight: 32.h,
                      ),
                      tooltip: 'Cancel download',
                    ),
                  if (onDismiss != null)
                    IconButton(
                      onPressed: onDismiss,
                      icon: Icon(Symbols.close, size: 18.sp),
                      iconSize: 18.sp,
                      constraints: BoxConstraints(
                        minWidth: 32.w,
                        minHeight: 32.h,
                      ),
                      tooltip: 'Dismiss',
                    ),
                ],
              ),
            ],
          ),
          
          // Progress bar and details (for downloading state)
          if (progress.state == DownloadState.downloading) ...[
            SizedBox(height: 12.h),
            LinearProgressIndicator(
              value: progress.progress,
              backgroundColor: colorScheme.outline.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
            SizedBox(height: 8.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  progress.progressPercentageString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
                Text(
                  progress.downloadedSizeString,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.7),
                  ),
                ),
              ],
            ),
            if (progress.speedBytesPerSecond != null ||
                progress.estimatedTimeRemainingSeconds != null) ...[
              SizedBox(height: 4.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (progress.speedBytesPerSecond != null)
                    Text(
                      progress.speedString,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                  if (progress.estimatedTimeRemainingSeconds != null)
                    Text(
                      '${progress.estimatedTimeRemainingString} remaining',
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.5),
                      ),
                    ),
                ],
              ),
            ],
          ],
          
          // Error message (for failed state)
          if (progress.state == DownloadState.failed && 
              progress.errorMessage != null) ...[
            SizedBox(height: 8.h),
            Text(
              progress.errorMessage!,
              style: theme.textTheme.bodySmall?.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusIcon(ColorScheme colorScheme) {
    switch (progress.state) {
      case DownloadState.downloading:
        return SizedBox(
          width: 24.w,
          height: 24.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            value: progress.progress,
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
        );
      case DownloadState.downloaded:
        return Container(
          width: 24.w,
          height: 24.w,
          decoration: BoxDecoration(
            color: Colors.green,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Symbols.check,
            size: 16.sp,
            color: Colors.white,
          ),
        );
      case DownloadState.failed:
        return Container(
          width: 24.w,
          height: 24.w,
          decoration: BoxDecoration(
            color: Colors.red,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Symbols.close,
            size: 16.sp,
            color: Colors.white,
          ),
        );
      case DownloadState.cancelled:
        return Container(
          width: 24.w,
          height: 24.w,
          decoration: BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Symbols.close,
            size: 16.sp,
            color: Colors.white,
          ),
        );
      case DownloadState.paused:
        return Container(
          width: 24.w,
          height: 24.w,
          decoration: BoxDecoration(
            color: Colors.orange,
            shape: BoxShape.circle,
          ),
          child: Icon(
            Symbols.pause,
            size: 16.sp,
            color: Colors.white,
          ),
        );
      case DownloadState.notDownloaded:
        return Icon(
          Symbols.cloud_download,
          size: 24.sp,
          color: colorScheme.primary,
        );
    }
  }

  String _getNotificationTitle() {
    switch (progress.state) {
      case DownloadState.downloading:
        return 'Downloading...';
      case DownloadState.downloaded:
        return 'Download Complete';
      case DownloadState.failed:
        return 'Download Failed';
      case DownloadState.cancelled:
        return 'Download Cancelled';
      case DownloadState.paused:
        return 'Download Paused';
      case DownloadState.notDownloaded:
        return 'Ready to Download';
    }
  }
}

/// Helper class for showing download notifications
class DownloadNotificationHelper {
  /// Show a download notification as a snackbar
  static void showDownloadSnackbar(
    BuildContext context,
    DownloadProgressModel progress,
    String fileName, {
    VoidCallback? onCancel,
  }) {
    final messenger = ScaffoldMessenger.of(context);
    
    messenger.showSnackBar(
      SnackBar(
        content: DownloadNotificationWidget(
          progress: progress,
          fileName: fileName,
          onCancel: onCancel,
          onDismiss: () => messenger.hideCurrentSnackBar(),
        ),
        backgroundColor: Colors.transparent,
        elevation: 0,
        behavior: SnackBarBehavior.floating,
        duration: progress.isInProgress 
            ? const Duration(days: 1) // Keep showing while downloading
            : const Duration(seconds: 4),
      ),
    );
  }

  /// Show a download notification as an overlay
  static OverlayEntry showDownloadOverlay(
    BuildContext context,
    DownloadProgressModel progress,
    String fileName, {
    VoidCallback? onCancel,
    VoidCallback? onDismiss,
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;
    
    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 16.h,
        left: 16.w,
        right: 16.w,
        child: Material(
          color: Colors.transparent,
          child: DownloadNotificationWidget(
            progress: progress,
            fileName: fileName,
            onCancel: onCancel,
            onDismiss: onDismiss ?? () => overlayEntry.remove(),
          ),
        ),
      ),
    );
    
    overlay.insert(overlayEntry);
    return overlayEntry;
  }
}
