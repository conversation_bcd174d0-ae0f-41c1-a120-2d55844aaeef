import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:material_symbols_icons/symbols.dart';
import '../models/download_progress_model.dart';
import '../enums/download_state.dart';

/// Widget for displaying download progress
class DownloadProgressWidget extends StatelessWidget {
  /// The download progress to display
  final DownloadProgressModel progress;
  
  /// Callback when cancel button is pressed
  final VoidCallback? onCancel;
  
  /// Callback when retry button is pressed
  final VoidCallback? onRetry;
  
  /// Whether to show detailed progress info
  final bool showDetails;

  const DownloadProgressWidget({
    super.key,
    required this.progress,
    this.onCancel,
    this.onRetry,
    this.showDetails = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: EdgeInsets.all(12.w),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8.r),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Progress indicator and status
          Row(
            children: [
              _buildProgressIndicator(colorScheme),
              SizedBox(width: 12.w),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      progress.state.label,
                      style: theme.textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    if (showDetails && progress.isInProgress) ...[
                      SizedBox(height: 4.h),
                      Text(
                        '${progress.progressPercentageString} • ${progress.downloadedSizeString}',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurface.withOpacity(0.6),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              _buildActionButton(colorScheme),
            ],
          ),
          
          // Progress bar (only for downloading state)
          if (progress.state == DownloadState.downloading) ...[
            SizedBox(height: 8.h),
            LinearProgressIndicator(
              value: progress.progress,
              backgroundColor: colorScheme.outline.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
          ],
          
          // Additional details (if enabled and relevant)
          if (showDetails && _shouldShowDetails()) ...[
            SizedBox(height: 8.h),
            _buildDetailsRow(theme),
          ],
        ],
      ),
    );
  }

  Widget _buildProgressIndicator(ColorScheme colorScheme) {
    switch (progress.state) {
      case DownloadState.downloading:
        return SizedBox(
          width: 20.w,
          height: 20.w,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            value: progress.progress,
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
        );
      case DownloadState.downloaded:
        return Icon(
          Symbols.check_circle,
          size: 20.sp,
          color: Colors.green,
        );
      case DownloadState.failed:
      case DownloadState.cancelled:
        return Icon(
          Symbols.error,
          size: 20.sp,
          color: Colors.red,
        );
      case DownloadState.paused:
        return Icon(
          Symbols.pause_circle,
          size: 20.sp,
          color: Colors.orange,
        );
      case DownloadState.notDownloaded:
        return Icon(
          Symbols.cloud_download,
          size: 20.sp,
          color: colorScheme.onSurface.withOpacity(0.6),
        );
    }
  }

  Widget _buildActionButton(ColorScheme colorScheme) {
    if (progress.state.canCancel && onCancel != null) {
      return IconButton(
        onPressed: onCancel,
        icon: Icon(Symbols.close, size: 16.sp),
        iconSize: 16.sp,
        constraints: BoxConstraints(
          minWidth: 24.w,
          minHeight: 24.h,
        ),
        padding: EdgeInsets.zero,
        tooltip: 'Cancel download',
      );
    }
    
    if (progress.state.canRetry && onRetry != null) {
      return IconButton(
        onPressed: onRetry,
        icon: Icon(Symbols.refresh, size: 16.sp),
        iconSize: 16.sp,
        constraints: BoxConstraints(
          minWidth: 24.w,
          minHeight: 24.h,
        ),
        padding: EdgeInsets.zero,
        tooltip: 'Retry download',
      );
    }
    
    return const SizedBox.shrink();
  }

  Widget _buildDetailsRow(ThemeData theme) {
    final details = <String>[];
    
    if (progress.speedBytesPerSecond != null) {
      details.add(progress.speedString);
    }
    
    if (progress.estimatedTimeRemainingSeconds != null) {
      details.add('${progress.estimatedTimeRemainingString} remaining');
    }
    
    if (progress.totalBytes != null) {
      details.add('of ${progress.totalSizeString}');
    }
    
    if (details.isEmpty) return const SizedBox.shrink();
    
    return Text(
      details.join(' • '),
      style: theme.textTheme.bodySmall?.copyWith(
        color: theme.colorScheme.onSurface.withOpacity(0.5),
      ),
    );
  }

  bool _shouldShowDetails() {
    return progress.isInProgress || 
           progress.hasFailed || 
           (progress.totalBytes != null && progress.totalBytes! > 0);
  }
}

/// Compact version of download progress widget for use in lists
class CompactDownloadProgressWidget extends StatelessWidget {
  /// The download progress to display
  final DownloadProgressModel progress;
  
  /// Callback when cancel button is pressed
  final VoidCallback? onCancel;

  const CompactDownloadProgressWidget({
    super.key,
    required this.progress,
    this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Progress indicator
        SizedBox(
          width: 16.w,
          height: 16.w,
          child: _buildProgressIndicator(colorScheme),
        ),
        
        SizedBox(width: 8.w),
        
        // Progress text
        if (progress.isInProgress)
          Text(
            progress.progressPercentageString,
            style: theme.textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        
        // Cancel button
        if (progress.state.canCancel && onCancel != null) ...[
          SizedBox(width: 4.w),
          GestureDetector(
            onTap: onCancel,
            child: Icon(
              Symbols.close,
              size: 14.sp,
              color: colorScheme.onSurface.withOpacity(0.6),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildProgressIndicator(ColorScheme colorScheme) {
    switch (progress.state) {
      case DownloadState.downloading:
        return CircularProgressIndicator(
          strokeWidth: 1.5,
          value: progress.progress,
          valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
        );
      case DownloadState.downloaded:
        return Icon(
          Symbols.check_circle,
          size: 16.sp,
          color: Colors.green,
        );
      case DownloadState.failed:
      case DownloadState.cancelled:
        return Icon(
          Symbols.error,
          size: 16.sp,
          color: Colors.red,
        );
      case DownloadState.paused:
        return Icon(
          Symbols.pause_circle,
          size: 16.sp,
          color: Colors.orange,
        );
      case DownloadState.notDownloaded:
        return Icon(
          Symbols.cloud_download,
          size: 16.sp,
          color: colorScheme.onSurface.withOpacity(0.6),
        );
    }
  }
}
